// © juliangonzaconde - Enhanced Volume Profile with Previous Day Levels

//@version=5
indicator("Enhanced VP-Period with PDH/PDL", overlay=true, max_bars_back=5000, max_lines_count=500, max_labels_count=100)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// INPUT SETTINGS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Previous Day Settings
show_pdh_pdl = input.bool(true, "Show Previous Day High/Low", group="Previous Day Levels")
pdh_color = input.color(color.new(color.yellow, 0), "PDH Color", group="Previous Day Levels")
pdl_color = input.color(color.new(color.yellow, 0), "PDL Color", group="Previous Day Levels")
extend_pdh_pdl = input.bool(true, "Extend PDH/PDL to Next Day", group="Previous Day Levels")

// Volume Profile Settings
profile_resolution = input.int(100, "Profile Resolution", minval=50, maxval=200, group="Volume Profile")
value_area_percent = input.float(70.0, "Value Area %", minval=50.0, maxval=95.0, step=5.0, group="Volume Profile")
show_volume_bars = input.bool(true, "Show Volume Bars", group="Volume Profile")
show_poc = input.bool(true, "Show POC Line", group="Volume Profile")
show_vah_val = input.bool(true, "Show VAH/VAL Lines", group="Volume Profile")
extend_levels = input.bool(true, "Extend POC/VAH/VAL to Next Day", group="Volume Profile")

// Colors
poc_color = input.color(color.new(color.white, 0), "POC Color", group="Colors")
vah_color = input.color(color.new(color.red, 0), "VAH Color", group="Colors")
val_color = input.color(color.new(color.green, 0), "VAL Color", group="Colors")
volume_bars_color = input.color(color.new(color.blue, 70), "Volume Bars Color", group="Colors")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// VARIABLES
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

var float prev_day_high = na
var float prev_day_low = na
var int prev_day_start_bar = na
var int prev_day_end_bar = na
var bool high_formed_first = false

// Volume profile variables
var array<float> volume_bins = array.new<float>()
var float poc_price = na
var float vah_price = na
var float val_price = na
var int profile_start_time = na
var int profile_end_time = na

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Function to get previous day's high and low
get_prev_day_levels() =>
    prev_high = request.security(syminfo.tickerid, "1D", high[1], barmerge.gaps_off, barmerge.lookahead_off)
    prev_low = request.security(syminfo.tickerid, "1D", low[1], barmerge.gaps_off, barmerge.lookahead_off)
    [prev_high, prev_low]

// Function to determine which extreme formed first after day open
determine_profile_direction(start_bar, end_bar, day_high, day_low) =>
    high_bar = na(int)
    low_bar = na(int)

    // Find when high and low were first reached
    for i = start_bar to end_bar
        if na(high_bar) and high[bar_index - (end_bar - i)] >= day_high
            high_bar := i
        if na(low_bar) and low[bar_index - (end_bar - i)] <= day_low
            low_bar := i
        if not na(high_bar) and not na(low_bar)
            break

    // Return true if high formed first
    not na(high_bar) and not na(low_bar) ? high_bar <= low_bar : not na(high_bar)

// Function to calculate volume profile
calculate_volume_profile(start_bar, end_bar, range_high, range_low, resolution) =>
    bins = array.new<float>(resolution, 0.0)
    price_step = (range_high - range_low) / resolution

    if price_step > 0
        for i = start_bar to end_bar
            bar_idx = bar_index - (end_bar - i)
            if bar_idx >= 0
                bar_high = high[bar_index - (end_bar - i)]
                bar_low = low[bar_index - (end_bar - i)]
                bar_volume = volume[bar_index - (end_bar - i)]

                // Distribute volume across touched price levels
                levels_touched = 0
                for j = 0 to resolution - 1
                    level_price = range_low + (j + 0.5) * price_step
                    if bar_low <= level_price and bar_high >= level_price
                        levels_touched += 1

                if levels_touched > 0
                    volume_per_level = bar_volume / levels_touched
                    for j = 0 to resolution - 1
                        level_price = range_low + (j + 0.5) * price_step
                        if bar_low <= level_price and bar_high >= level_price
                            current_vol = array.get(bins, j)
                            array.set(bins, j, current_vol + volume_per_level)
    bins

// Function to find Point of Control
find_poc(volume_bins) =>
    max_volume = 0.0
    poc_level = 0

    for i = 0 to array.size(volume_bins) - 1
        vol = array.get(volume_bins, i)
        if vol > max_volume
            max_volume := vol
            poc_level := i

    [poc_level, max_volume]

// Function to calculate Value Area High and Low
calculate_value_area(volume_bins, poc_level, target_percent) =>
    vah_level = poc_level
    val_level = poc_level

    if array.size(volume_bins) > 0 and poc_level >= 0 and poc_level < array.size(volume_bins)
        total_volume = 0.0
        for i = 0 to array.size(volume_bins) - 1
            total_volume += array.get(volume_bins, i)

        if total_volume > 0
            target_volume = total_volume * (target_percent / 100.0)
            value_area_volume = array.get(volume_bins, poc_level)

            up_level = poc_level
            down_level = poc_level

            while value_area_volume < target_volume and (up_level < array.size(volume_bins) - 1 or down_level > 0)
                up_vol = up_level < array.size(volume_bins) - 1 ? array.get(volume_bins, up_level + 1) : 0.0
                down_vol = down_level > 0 ? array.get(volume_bins, down_level - 1) : 0.0

                if up_vol >= down_vol and up_level < array.size(volume_bins) - 1
                    up_level += 1
                    value_area_volume += up_vol
                    vah_level := up_level
                else if down_level > 0
                    down_level -= 1
                    value_area_volume += down_vol
                    val_level := down_level
                else
                    break

    [vah_level, val_level]

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// MAIN LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Get current day's time
daily_time = time("1D")
is_new_day = ta.change(daily_time) != 0

// Get previous day levels
[pdh, pdl] = get_prev_day_levels()

// Store previous day levels when new day starts
if is_new_day and not na(pdh) and not na(pdl)
    prev_day_high := pdh
    prev_day_low := pdl
    prev_day_end_bar := bar_index - 1

    // Estimate previous day start (for 1-minute chart, approximately 1440 bars per day)
    bars_per_day = timeframe.isintraday ? math.round(1440 / timeframe.multiplier) : 1
    prev_day_start_bar := math.max(0, prev_day_end_bar - bars_per_day)

    // Determine profile direction based on which extreme formed first
    if not na(prev_day_start_bar) and not na(prev_day_end_bar)
        high_formed_first := determine_profile_direction(prev_day_start_bar, prev_day_end_bar, prev_day_high, prev_day_low)

// Calculate volume profile on new day or last bar
if (is_new_day or barstate.islast) and not na(prev_day_high) and not na(prev_day_low) and not na(prev_day_start_bar) and not na(prev_day_end_bar)

    // Determine profile range based on which extreme formed first
    profile_high = high_formed_first ? prev_day_high : prev_day_low
    profile_low = high_formed_first ? prev_day_low : prev_day_high

    // Calculate volume profile
    volume_bins := calculate_volume_profile(prev_day_start_bar, prev_day_end_bar, profile_high, profile_low, profile_resolution)

    if array.size(volume_bins) > 0
        // Find POC
        [poc_level, max_volume] = find_poc(volume_bins)

        if max_volume > 0
            // Calculate VAH and VAL
            [vah_level, val_level] = calculate_value_area(volume_bins, poc_level, value_area_percent)

            // Convert levels to prices
            price_step = (profile_high - profile_low) / profile_resolution
            poc_price := profile_low + (poc_level + 0.5) * price_step
            vah_price := profile_low + (vah_level + 0.5) * price_step
            val_price := profile_low + (val_level + 0.5) * price_step

            profile_start_time := time[bar_index - prev_day_end_bar]
            profile_end_time := extend_levels ? time + 86400000 : time

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// DRAWING
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Draw volume profile bars
if barstate.islast and not na(prev_day_high) and not na(prev_day_low) and array.size(volume_bins) > 0 and show_volume_bars
    profile_high = high_formed_first ? prev_day_high : prev_day_low
    profile_low = high_formed_first ? prev_day_low : prev_day_high
    price_step = (profile_high - profile_low) / profile_resolution

    [poc_level, max_volume] = find_poc(volume_bins)

    if max_volume > 0
        // Calculate bar width based on time
        bar_width = math.round((profile_end_time - profile_start_time) * 0.3)

        // Draw volume bars
        for i = 0 to array.size(volume_bins) - 1
            vol = array.get(volume_bins, i)
            if vol > 0
                level_price = profile_low + (i + 0.5) * price_step
                normalized_vol = vol / max_volume
                bar_end_time = profile_start_time + math.round(bar_width * normalized_vol)

                // Draw every 5th bar to avoid clutter
                if i % 5 == 0
                    line.new(profile_start_time, level_price, bar_end_time, level_price,
                             color=volume_bars_color, width=2, xloc=xloc.bar_time)

// Draw POC line
if show_poc and not na(poc_price)
    line.new(profile_start_time, poc_price, profile_end_time, poc_price,
             color=poc_color, width=3, style=line.style_solid, xloc=xloc.bar_time)
    label.new(profile_end_time, poc_price, "POC", style=label.style_label_left,
              color=color.new(color.white, 100), textcolor=poc_color, size=size.small, xloc=xloc.bar_time)

// Draw VAH line
if show_vah_val and not na(vah_price)
    line.new(profile_start_time, vah_price, profile_end_time, vah_price,
             color=vah_color, width=2, style=line.style_dashed, xloc=xloc.bar_time)
    label.new(profile_end_time, vah_price, "VAH", style=label.style_label_left,
              color=color.new(color.white, 100), textcolor=vah_color, size=size.small, xloc=xloc.bar_time)

// Draw VAL line
if show_vah_val and not na(val_price)
    line.new(profile_start_time, val_price, profile_end_time, val_price,
             color=val_color, width=2, style=line.style_dashed, xloc=xloc.bar_time)
    label.new(profile_end_time, val_price, "VAL", style=label.style_label_left,
              color=color.new(color.white, 100), textcolor=val_color, size=size.small, xloc=xloc.bar_time)

// Draw Previous Day High and Low
if show_pdh_pdl and not na(prev_day_high) and not na(prev_day_low)
    pdh_end_time = extend_pdh_pdl ? time + 86400000 : time

    // PDH line
    line.new(profile_start_time, prev_day_high, pdh_end_time, prev_day_high,
             color=pdh_color, width=2, style=line.style_solid, xloc=xloc.bar_time)
    label.new(pdh_end_time, prev_day_high, "PDH", style=label.style_label_left,
              color=color.new(color.white, 100), textcolor=pdh_color, size=size.small, xloc=xloc.bar_time)

    // PDL line
    line.new(profile_start_time, prev_day_low, pdh_end_time, prev_day_low,
             color=pdl_color, width=2, style=line.style_solid, xloc=xloc.bar_time)
    label.new(pdh_end_time, prev_day_low, "PDL", style=label.style_label_left,
              color=color.new(color.white, 100), textcolor=pdl_color, size=size.small, xloc=xloc.bar_time)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Alert conditions
alertcondition(close >= prev_day_high and not na(prev_day_high), "Price reached PDH", "Price has reached Previous Day High")
alertcondition(close <= prev_day_low and not na(prev_day_low), "Price reached PDL", "Price has reached Previous Day Low")
alertcondition(close >= poc_price and not na(poc_price), "Price reached POC", "Price has reached Point of Control")
alertcondition(close >= vah_price and not na(vah_price), "Price reached VAH", "Price has reached Value Area High")
alertcondition(close <= val_price and not na(val_price), "Price reached VAL", "Price has reached Value Area Low")